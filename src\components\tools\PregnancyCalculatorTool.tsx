

'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Baby } from 'lucide-react';
import { format, addDays, differenceInWeeks, differenceInDays } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب'),
  month: requiredNumber('الشهر مطلوب'),
  year: requiredNumber('السنة مطلوبة'),
}).refine(data => {
    try {
        const date = new Date(data.year, data.month - 1, data.day);
        return date.getFullYear() === data.year && date.getMonth() === data.month - 1 && date.getDate() === data.day && date <= new Date();
    } catch {
        return false;
    }
}, {
    message: 'تاريخ غير صالح أو في المستقبل.',
    path: ['day'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  dueDate: Date;
  currentWeek: number;
  remainingDays: number;
}

const DateFields = ({ control }: { control: Control<FormValues> }) => {
    const years = Array.from({ length: 3 }, (_, i) => new Date().getFullYear() - i);
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const days = Array.from({ length: 31 }, (_, i) => i + 1);

    return (
    <div className="grid grid-cols-3 gap-4">
        <FormField
            control={control}
            name="day"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>اليوم</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="يوم" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {days.map(day => <SelectItem key={day} value={String(day)}>{day}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="month"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>الشهر</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="شهر" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {months.map(month => <SelectItem key={month} value={String(month)}>{month}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="year"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>السنة</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="سنة" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {years.map(year => <SelectItem key={year} value={String(year)}>{year}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
    </div>
)};


export function PregnancyCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
        day: undefined,
        month: undefined,
        year: undefined
    }
  });

  function onSubmit(data: FormValues) {
    const lmp = new Date(data.year, data.month - 1, data.day);
    const dueDate = addDays(lmp, 280);
    const currentWeek = differenceInWeeks(new Date(), lmp);
    const remainingDays = differenceInDays(dueDate, new Date());
    
    setResult({ dueDate, currentWeek, remainingDays });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة الحمل والولادة</CardTitle>
        <CardDescription>أدخلي تاريخ أول يوم من آخر دورة شهرية لتوقّع موعد ولادتك.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormLabel>أول يوم من آخر دورة شهرية (LMP)</FormLabel>
            <DateFields control={form.control} />
            <Button type="submit" className="w-full">احسبي موعد الولادة</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">النتائج التقديرية</h3>
             <div className="p-6 bg-primary/10 rounded-lg mb-4">
                <Baby className="w-12 h-12 text-primary mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">موعد الولادة المتوقع</p>
                <p className="text-3xl font-bold font-mono text-primary">
                  {format(result.dueDate, 'EEEE, d MMMM yyyy', { locale: ar })}
                </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-3xl font-bold font-mono">{result.currentWeek}</p>
                <p className="text-sm text-muted-foreground">أنتِ في الأسبوع</p>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-3xl font-bold font-mono">{result.remainingDays}</p>
                <p className="text-sm text-muted-foreground">يومًا متبقيًا (تقريبًا)</p>
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
                هذه مجرد تقديرات. موعد الولادة الفعلي قد يختلف. استشيري طبيبكِ دائمًا.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
