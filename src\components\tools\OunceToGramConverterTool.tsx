
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Weight, Gem } from 'lucide-react';

const TROY_OUNCE_IN_GRAMS = 31.1034768;

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  ounces: requiredNumber().min(0, { message: 'الرجاء إدخال قيمة موجبة.' }).default(1),
});

export function OunceToGramConverterTool() {
  const [grams, setGrams] = useState<number>(TROY_OUNCE_IN_GRAMS);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      ounces: 1,
    },
  });

  const watchOunces = form.watch('ounces');

  useEffect(() => {
    if (form.formState.isValid) {
      const result = watchOunces * TROY_OUNCE_IN_GRAMS;
      setGrams(result);
    }
  }, [watchOunces, form.formState.isValid]);
  

  function onSubmit(data: z.infer<typeof FormSchema>) {
     const result = data.ounces * TROY_OUNCE_IN_GRAMS;
     setGrams(result);
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>اونصة الذهب كم جرام</CardTitle>
        <CardDescription>
          حوّل بسهولة بين أونصة تروي (وحدة قياس المعادن الثمينة) والجرام.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل المعتمد</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 أونصة تروي = {TROY_OUNCE_IN_GRAMS.toFixed(4)} جرام
            </p>
        </div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="ounces"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الكمية بالأونصة (تروي)</FormLabel>
                  <FormControl>
                    <div className="relative">
                        <Gem className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                        <Input type="number" {...field} className="pl-10" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
             <Button type="submit" className="w-full">
              تحويل
            </Button>
          </form>
        </Form>
        
        <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-2">النتيجة بالجرامات</h3>
            <div className="p-6 bg-primary/10 rounded-lg flex items-center justify-center gap-4">
                <Weight className="h-10 w-10 text-primary" />
                <div className="flex items-baseline gap-2">
                    <p className="text-5xl font-bold font-mono text-primary">
                        {grams.toLocaleString('ar-SA', { maximumFractionDigits: 4, numberingSystem: 'latn' })}
                    </p>
                    <span className="text-xl font-medium text-primary/80">جرام</span>
                </div>
            </div>
        </div>

      </CardContent>
    </Card>
  );
}
