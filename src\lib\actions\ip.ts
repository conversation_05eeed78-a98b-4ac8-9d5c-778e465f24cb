
'use server';

import { headers } from 'next/headers';
import { COUNTRY_PHONE_CODES } from '../constants/currencies';

interface IpInfo {
  ip: string;
  countryCode?: string;
  phoneCode?: string;
  error?: string;
}

export async function getIpInfo(): Promise<IpInfo> {
  const headersList = await headers();
  let ipAddress = headersList.get('x-forwarded-for')?.split(',')[0].trim() || headersList.get('x-real-ip');

  // If no IP from headers, try an external service just to get the IP
  if (!ipAddress) {
    try {
        const ipRes = await fetch('https://api.ipify.org?format=json', { cache: 'no-store' });
        if (ipRes.ok) {
            const ipData = await ipRes.json();
            ipAddress = ipData.ip;
        }
    } catch (e) {
      // Ignore error, will fallback later
    }
  }

  // If we have an IP, try to get geolocation info
  if (ipAddress) {
    try {
      // Switched to a more reliable geolocation service
      const geoResponse = await fetch(`http://ip-api.com/json/${ipAddress}`, {
         cache: 'no-store',
      });
      if (geoResponse.ok) {
        const geoData = await geoResponse.json();
        if (geoData.status === 'success' && geoData.countryCode) {
          return { 
            ip: ipAddress, 
            countryCode: geoData.countryCode,
            phoneCode: COUNTRY_PHONE_CODES[geoData.countryCode]
          };
        }
      }
    } catch (error) {
      console.error('Error fetching geolocation from ip-api.com:', error);
    }
  }
  
  // Fallback if anything fails
  return { ip: ipAddress || 'لا يمكن تحديده', countryCode: 'SA', phoneCode: '966', error: 'Could not determine location, defaulting to SA' };
}
