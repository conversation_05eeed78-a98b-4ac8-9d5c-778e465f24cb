import { notFound } from 'next/navigation';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent } from '@/components/ui/card';
import { Metadata } from 'next';

const pages = {
  'privacy-policy': {
    title: 'سياسة الخصوصية',
    content: `
      <p>تاريخ السريان: ${new Date().toLocaleDateString('ar-SA')}</p>
      <p>نحن في "جامع الأدوات" نحترم خصوصيتك ونلتزم بحمايتها. توضح سياسة الخصوصية هذه كيفية جمعنا واستخدامنا وحمايتنا لمعلوماتك عند زيارتك لموقعنا.</p>
      <h3 class="mt-4 font-bold">المعلومات التي نجمعها</h3>
      <p>قد نجمع المعلومات التي تقدمها لنا مباشرة، مثل عند استخدامك لأدوات تتطلب إدخال بيانات. هذه البيانات تستخدم فقط لتقديم الخدمة المطلوبة ولا يتم تخزينها على خوادمنا.</p>
      <h3 class="mt-4 font-bold">البيانات التي تتم معالجتها من جانب العميل</h3>
      <p>معظم أدواتنا تقوم بمعالجة البيانات مباشرة في متصفحك (من جانب العميل). هذا يعني أن البيانات التي تدخلها لا تغادر جهازك أبدًا، مما يوفر أقصى درجات الخصوصية.</p>
      <h3 class="mt-4 font-bold">ملفات تعريف الارتباط (Cookies)</h3>
      <p>قد نستخدم ملفات تعريف الارتباط لتحسين تجربة المستخدم وتحليل حركة المرور على الموقع. يمكنك التحكم في ملفات تعريف الارتباط من خلال إعدادات متصفحك.</p>
      <h3 class="mt-4 font-bold">التغييرات على سياسة الخصوصية</h3>
      <p>قد نقوم بتحديث سياسة الخصوصية هذه من وقت لآخر. سنقوم بإعلامك بأي تغييرات عن طريق نشر السياسة الجديدة على هذه الصفحة.</p>
    `,
  },
  'terms-of-service': {
    title: 'شروط الخدمة',
    content: `
      <p>تاريخ السريان: ${new Date().toLocaleDateString('ar-SA')}</p>
      <p>مرحبًا بك في "جامع الأدوات". باستخدامك لموقعنا، فإنك توافق على الالتزام بشروط وأحكام الخدمة التالية.</p>
      <h3 class="mt-4 font-bold">استخدام الأدوات</h3>
      <p>يتم توفير الأدوات على هذا الموقع "كما هي" ولأغراض إعلامية وتعليمية. نحن لا نضمن دقة النتائج بشكل مطلق ويجب استخدامها كدليل إرشادي. لا نتحمل أي مسؤولية عن أي قرارات يتم اتخاذها بناءً على نتائج هذه الأدوات.</p>
      <h3 class="mt-4 font-bold">الملكية الفكرية</h3>
      <p>المحتوى والتصميم والأدوات على هذا الموقع هي ملك لـ"جامع الأدوات" ومحمية بموجب قوانين حقوق النشر.</p>
      <h3 class="mt-4 font-bold">إخلاء المسؤولية</h3>
      <p>الأدوات والمعلومات المقدمة ليست بديلاً عن المشورة المهنية (المالية، القانونية، الطبية، إلخ). استشر دائمًا خبيرًا متخصصًا قبل اتخاذ أي قرارات هامة.</p>
    `,
  },
};

type Props = {
  params: { slug: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const page = pages[params.slug as keyof typeof pages];
  if (!page) {
    return {};
  }
  return {
    title: page.title,
    robots: {
      index: false, // Don't index policy pages
      follow: true,
    },
  };
}

export default function PolicyPage({ params }: Props) {
  const page = pages[params.slug as keyof typeof pages];

  if (!page) {
    notFound();
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-4 sm:p-6 md:p-8">
      <PageHeader title={page.title} description="" />
      <Card>
        <CardContent className="p-6">
          <div
            className="prose prose-lg max-w-none space-y-4"
            dangerouslySetInnerHTML={{ __html: page.content }}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export async function generateStaticParams() {
  return Object.keys(pages).map((slug) => ({
    slug,
  }));
}
