

'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Target, Leaf } from 'lucide-react';
import { format, addDays } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب'),
  month: requiredNumber('الشهر مطلوب'),
  year: requiredNumber('السنة مطلوبة'),
  cycleLength: requiredNumber('طول الدورة مطلوب').int().min(20, "طول الدورة عادة بين 20-45 يومًا.").max(45, "طول الدورة عادة بين 20-45 يومًا.").default(28),
}).refine(data => {
    try {
        const date = new Date(data.year, data.month - 1, data.day);
        return date.getFullYear() === data.year && date.getMonth() === data.month - 1 && date.getDate() === data.day && date <= new Date();
    } catch {
        return false;
    }
}, {
    message: 'تاريخ غير صالح أو في المستقبل.',
    path: ['day'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  ovulationDate: Date;
  fertileWindowStart: Date;
  fertileWindowEnd: Date;
  nextPeriodDate: Date;
}

const DateFields = ({ control }: { control: Control<FormValues> }) => {
    const years = Array.from({ length: 3 }, (_, i) => new Date().getFullYear() - i);
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const days = Array.from({ length: 31 }, (_, i) => i + 1);

    return (
    <div className="space-y-2">
        <FormLabel>أول يوم من آخر دورة شهرية</FormLabel>
        <div className="grid grid-cols-3 gap-2">
            <FormField
              control={control}
              name="day"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} defaultValue={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="اليوم" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {days.map(d => <SelectItem key={d} value={String(d)}>{d}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage className="col-span-3"/>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="month"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} defaultValue={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="الشهر" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {months.map(m => <SelectItem key={m} value={String(m)}>{m}</SelectItem>)}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="year"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} defaultValue={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="السنة" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {years.map(y => <SelectItem key={y} value={String(y)}>{y}</SelectItem>)}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
        </div>
    </div>
)};

export function OvulationCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      cycleLength: 28,
      day: undefined,
      month: undefined,
      year: undefined
    },
  });

  function onSubmit(data: FormValues) {
    const lastPeriodDate = new Date(data.year, data.month - 1, data.day);
    const { cycleLength } = data;
    // Ovulation is typically 14 days before the next period
    const ovulationDate = addDays(lastPeriodDate, cycleLength - 14);
    const fertileWindowStart = subDays(ovulationDate, 5);
    const fertileWindowEnd = addDays(ovulationDate, 1);
    const nextPeriodDate = addDays(lastPeriodDate, cycleLength);

    setResult({
      ovulationDate,
      fertileWindowStart,
      fertileWindowEnd,
      nextPeriodDate
    });
  }
  
  const subDays = (date: Date, days: number) => {
    const newDate = new Date(date);
    newDate.setDate(date.getDate() - days);
    return newDate;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة التبويض</CardTitle>
        <CardDescription>توقعي أيام الخصوبة لديك لزيادة فرص الحمل.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
               <DateFields control={form.control} />
               <FormField name="cycleLength" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>متوسط طول الدورة (بالأيام)</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
              )}/>
            </div>
            <Button type="submit" className="w-full">احسبي أيام التبويض</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">النتائج التقديرية</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                    <Leaf className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">نافذة الخصوبة</p>
                    <p className="text-lg font-bold font-mono text-green-700">
                        {format(result.fertileWindowStart, 'd MMMM', { locale: ar })} - {format(result.fertileWindowEnd, 'd MMMM', { locale: ar })}
                    </p>
                </div>
                <div className="p-4 bg-red-500/10 rounded-lg border border-red-500/20">
                    <Target className="w-8 h-8 text-red-600 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">يوم التبويض المتوقع</p>
                    <p className="text-lg font-bold font-mono text-red-700">
                        {format(result.ovulationDate, 'EEEE, d MMMM yyyy', { locale: ar })}
                    </p>
                </div>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
                هذه النتائج هي مجرد تقديرات. تختلف دورة كل امرأة عن الأخرى.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
