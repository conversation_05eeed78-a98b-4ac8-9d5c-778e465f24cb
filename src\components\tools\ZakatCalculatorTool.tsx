
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Coins, Info } from 'lucide-react';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  cash: requiredNumber().nonnegative().default(0),
  goldSilver: requiredNumber().nonnegative().default(0),
  investments: requiredNumber().nonnegative().default(0),
  businessAssets: requiredNumber().nonnegative().default(0),
  debts: requiredNumber().nonnegative().default(0),
});

const NISAB_SAR = 20400; // Based on 85 grams of gold, e.g., 85 * 240 SAR/gram

interface Result {
  totalWealth: number;
  isNisabMet: boolean;
  zakatDue: number;
}

export function ZakatCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      cash: 0,
      goldSilver: 0,
      investments: 0,
      businessAssets: 0,
      debts: 0,
    },
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const totalAssets = data.cash + data.goldSilver + data.investments + data.businessAssets;
    const zakatableWealth = totalAssets - data.debts;
    const isNisabMet = zakatableWealth >= NISAB_SAR;
    const zakatDue = isNisabMet ? zakatableWealth * 0.025 : 0;

    setResult({
      totalWealth: zakatableWealth,
      isNisabMet,
      zakatDue,
    });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب زكاة المال</CardTitle>
        <CardDescription>أدخل قيمة أصولك وديونك لحساب الزكاة الواجبة.</CardDescription>
      </CardHeader>
      <CardContent>
        <Alert className="mb-6">
          <Info className="h-4 w-4" />
          <AlertTitle>نصاب الزكاة</AlertTitle>
          <AlertDescription>
            يتم حساب النصاب بناءً على قيمة 85 جرامًا من الذهب. القيمة الحالية المستخدمة في هذه الحاسبة هي{" "}
            <span className="font-bold">{NISAB_SAR.toLocaleString('ar-SA', { numberingSystem: 'latn' })} ريال سعودي</span>.
            يجب أن تكون الأصول قد حال عليها الحول (عام هجري كامل).
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="cash"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>النقد (في اليد والبنك)</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="goldSilver"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>قيمة الذهب والفضة</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="investments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>قيمة الأسهم والاستثمارات</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="businessAssets"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>قيمة عروض التجارة</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="debts"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>الديون المستحقة عليك</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit" className="w-full">
              <Coins className="ml-2 h-4 w-4" />
              احسب الزكاة
            </Button>
          </form>
        </Form>

        {result && (
          <div className="mt-8 text-center space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-sm text-muted-foreground">وعاء الزكاة (بعد خصم الديون)</p>
                <p className="text-2xl font-bold font-mono text-primary">
                  {result.totalWealth.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', numberingSystem: 'latn' })}
                </p>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-sm text-muted-foreground">مقدار الزكاة الواجبة</p>
                <p className="text-2xl font-bold font-mono text-primary">
                  {result.zakatDue.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', numberingSystem: 'latn' })}
                </p>
              </div>
            </div>
            {!result.isNisabMet && (
              <Alert variant="default">
                <Info className="h-4 w-4" />
                <AlertTitle>لم تبلغ النصاب</AlertTitle>
                <AlertDescription>
                  مجموع أموالك أقل من قيمة النصاب، لذا لا تجب عليك الزكاة حاليًا.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
