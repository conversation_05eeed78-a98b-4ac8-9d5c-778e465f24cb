
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toolCategories } from '@/lib/tools';
import { Button } from '@/components/ui/button';
import { ChevronDown, Home } from 'lucide-react';

export function AppSidebar() {
  const pathname = usePathname();

  return (
    <Sidebar side="right">
      <SidebarContent className="p-0 pt-4">
        <SidebarMenu>
          {toolCategories.map((category, index) => (
            <SidebarMenuItem key={category.name}>
              <Collapsible defaultOpen={index < 3}>
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-between h-auto p-2 text-sm font-headline text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground group"
                  >
                    <div className="flex items-center gap-3">
                      <category.icon className="h-5 w-5 text-sidebar-primary" />
                      {category.name}
                    </div>
                    <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="px-3 pb-2">
                  <SidebarMenu>
                    {category.tools.map((tool) => {
                      const ToolIcon = tool.icon;
                      return (
                        <SidebarMenuItem key={tool.path}>
                          {tool.component ? (
                            <SidebarMenuButton
                              asChild
                              className="w-full justify-start gap-2"
                              variant="default"
                              size="sm"
                              isActive={pathname === tool.path}
                            >
                              <Link href={tool.path}>
                                {ToolIcon && <ToolIcon className="text-sidebar-primary/80" />}
                                {tool.name}
                              </Link>
                            </SidebarMenuButton>
                          ) : (
                            <SidebarMenuButton
                              className="w-full justify-start gap-2"
                              variant="default"
                              size="sm"
                              disabled
                            >
                              {ToolIcon && <ToolIcon className="text-sidebar-primary/50" />}
                              {tool.name}
                            </SidebarMenuButton>
                          )}
                        </SidebarMenuItem>
                      );
                    })}
                  </SidebarMenu>
                </CollapsibleContent>
              </Collapsible>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <Link href="/">
          <Button variant="outline" className="w-full bg-transparent border-sidebar-border text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
            <Home className="ml-2 h-4 w-4" />
            العودة للصفحة الرئيسية
          </Button>
        </Link>
      </SidebarFooter>
    </Sidebar>
  );
}
