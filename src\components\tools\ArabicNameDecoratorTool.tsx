
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Copy, Wand2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Switch } from '../ui/switch';
import { ScrollArea } from '../ui/scroll-area';

const FormSchema = z.object({
  text: z.string().min(1, { message: 'الرجاء إدخال اسم للزخرفة.' }),
  addKashida: z.boolean().default(false),
});

const applyKashida = (text: string): string => {
  const kashida = 'ـ';
  // Avoid adding kashida before or after spaces, or after characters that don't connect forwards.
  const noConnectChars = 'اأإآدذرزوؤ';
  let result = '';
  for (let i = 0; i < text.length; i++) {
    result += text[i];
    if (
      i < text.length - 1 &&
      text[i] !== ' ' &&
      text[i + 1] !== ' ' &&
      !noConnectChars.includes(text[i])
    ) {
      result += kashida;
    }
  }
  return result;
};

const decorationMaps: { [key: string]: { [key: string]: string } } = {
  symbols1: {'ا':'Ã','ب':'β','ت':'Ť','ث':'ŤĤ','ج':'Ĵ','ح':'Ĥ','خ':'ĶĤ','د':'Ď','ذ':'ĎĤ','ر':'Ř','ز':'Ż','س':'Ŝ','ش':'ŜĤ','ص':'Š','ض':'Ď','ط':'Ŧ','ظ':'Ż','ع':'Ă','غ':'Ğ','ف':'Ŧ','ق':'Q','ك':'Ķ','ل':'Ĺ','م':'M','ن':'Ň','ه':'Ĥ','و':'Ŵ','ي':'Ў','ة':'─','أ':'Å','إ':'Ễ','آ':'Ā'},
  symbols2: {'ا':'Ạ','ب':'B','ت':'T','ث':'T','ج':'J','ح':'H','خ':'K','د':'D','ذ':'D','ر':'R','ز':'Z','س':'S','ش':'S','ص':'S','ض':'D','ط':'T','ظ':'Z','ع':'A','غ':'G','ف':'F','ق':'Q','ك':'K','ل':'L','م':'M','ن':'N','ه':'H','و':'W','ي':'Y','ة':'A','أ':'A','إ':'E','آ':'A'},
  circles: {'ا':'ⓐ','ب':'ⓑ','ت':'ⓣ','ث':'ⓣ','ج':'ⓙ','ح':'ⓗ','خ':'ⓚ','د':'ⓓ','ذ':'ⓓ','ر':'ⓡ','ز':'ⓩ','س':'ⓢ','ش':'ⓢ','ص':'ⓢ','ض':'ⓓ','ط':'ⓣ','ظ':'ⓩ','ع':'ⓐ','غ':'ⓖ','ف':'ⓕ','ق':'ⓠ','ك':'ⓚ','ل':'ⓛ','م':'ⓜ','ن':'ⓝ','ه':'ⓗ','و':'w','ي':'ⓨ','ة':'ⓐ','أ':'ⓐ','إ':'ⓔ','آ':'ⓐ'},
};

const applyDecoration = (text: string, map: { [key: string]: string }): string => {
  return [...text].map(char => map[char] || char).join('');
};

const surrounders: { [key: string]: [string, string] } = {
  sparkles: ['✨', '✨'],
  hearts: ['💖', '💖'],
  stars: ['⭐', '⭐'],
  doubleArrow: ['《', '》'],
  curved: ['︵', '︶'],
};

export function ArabicNameDecoratorTool() {
  const [results, setResults] = useState<string[]>([]);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      text: '',
      addKashida: false,
    },
  });

  const onSubmit = (data: z.infer<typeof FormSchema>) => {
    let baseText = data.text;
    if (data.addKashida) {
      baseText = applyKashida(baseText);
    }
    
    const decoratedResults: string[] = [];

    // Basic text
    decoratedResults.push(baseText);

    // Decorations
    Object.values(decorationMaps).forEach(map => {
        decoratedResults.push(applyDecoration(baseText, map));
    });

    // Surrounders
    Object.values(surrounders).forEach(([start, end]) => {
        decoratedResults.push(`${start}${baseText}${end}`);
    });
    
    // Combined
    decoratedResults.push(`${surrounders.sparkles[0]}${applyDecoration(baseText, decorationMaps.symbols1)}${surrounders.sparkles[1]}`);


    setResults(decoratedResults);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: 'تم النسخ بنجاح!',
        description: `"${text}" تم نسخه إلى الحافظة.`,
      });
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>زخرفة الأسماء العربية</CardTitle>
        <CardDescription>
          أدخل اسمك أو أي نص عربي لترى مجموعة متنوعة من الأنماط المزخرفة الجاهزة للنسخ.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
              <FormField
                control={form.control}
                name="text"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>النص الأصلي</FormLabel>
                    <FormControl>
                      <Input placeholder="اكتب الاسم هنا" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" size="lg" className="w-full">
                <Wand2 className="ml-2 h-4 w-4" />
                زخرفة
              </Button>
            </div>
            <FormField
              control={form.control}
              name="addKashida"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>تطويل الحروف (كشيدة)</FormLabel>
                    <FormDescription>
                      إضافة تطويل بين الحروف لإعطاء شكل جمالي.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      dir="ltr"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>
        
        {results.length > 0 && (
          <div className="mt-8">
            <h3 className="text-xl font-headline font-semibold mb-4">النتائج:</h3>
            <ScrollArea className="h-72 w-full rounded-md border p-4 bg-muted/50">
              <div className="space-y-3">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between gap-4 p-3 bg-background rounded-md shadow-sm">
                    <p className="text-lg font-mono flex-1 text-right" dir="rtl">{result}</p>
                    <Button variant="outline" size="icon" onClick={() => copyToClipboard(result)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
