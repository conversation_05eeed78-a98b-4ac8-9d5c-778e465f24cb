
'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ArrowRightLeft } from 'lucide-react';

const MILE_TO_KM_FACTOR = 1.60934;

export function MileKilometerConverterTool() {
  const [miles, setMiles] = useState('1');
  const [kilometers, setKilometers] = useState(MILE_TO_KM_FACTOR.toString());

  const handleMileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMiles(value);
    if (value && !isNaN(parseFloat(value))) {
      const kmValue = parseFloat(value) * MILE_TO_KM_FACTOR;
      setKilometers(kmValue.toLocaleString('en-US', {maximumFractionDigits: 3, useGrouping: false}));
    } else {
      setKilometers('');
    }
  };

  const handleKilometerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKilometers(value);
    if (value && !isNaN(parseFloat(value))) {
      const mileValue = parseFloat(value) / MILE_TO_KM_FACTOR;
      setMiles(mileValue.toLocaleString('en-US', {maximumFractionDigits: 3, useGrouping: false}));
    } else {
      setMiles('');
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تحويل من ميل إلى كيلو (والعكس)</CardTitle>
        <CardDescription>
          أدخل القيمة في أي من الحقلين لرؤية التحويل الفوري.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 ميل = 1.60934 كيلومتر
            </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="w-full">
            <label htmlFor="miles" className="text-sm font-medium mb-2 block">
              ميل (Mile)
            </label>
            <Input
              id="miles"
              type="number"
              value={miles}
              onChange={handleMileChange}
              placeholder="أدخل الأميال"
              dir="ltr"
            />
          </div>
          
          <div className="shrink-0 pt-6">
            <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
          </div>

          <div className="w-full">
            <label htmlFor="kilometers" className="text-sm font-medium mb-2 block">
              كيلومتر (Kilometer)
            </label>
            <Input
              id="kilometers"
              type="number"
              value={kilometers}
              onChange={handleKilometerChange}
              placeholder="أدخل الكيلومترات"
              dir="ltr"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
