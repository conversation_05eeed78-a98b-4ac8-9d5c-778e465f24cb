
'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { intervalToDuration, isAfter, parse } from 'date-fns';
import { Users } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const dateSchema = z.object({
  day: requiredNumber('اليوم مطلوب'),
  month: requiredNumber('الشهر مطلوب'),
  year: requiredNumber('السنة مطلوبة'),
});

const FormSchema = z.object({
  person1: dateSchema,
  person2: dateSchema,
}).refine(data => {
  try {
    const date1 = new Date(data.person1.year, data.person1.month - 1, data.person1.day);
    const date2 = new Date(data.person2.year, data.person2.month - 1, data.person2.day);
    return date1.getFullYear() === data.person1.year && date1.getMonth() === data.person1.month - 1 && date1.getDate() === data.person1.day
        && date2.getFullYear() === data.person2.year && date2.getMonth() === data.person2.month - 1 && date2.getDate() === data.person2.day;
  } catch {
    return false;
  }
}, {
    message: 'أحد التواريخ المدخلة غير صالح.',
    path: ['person1', 'day'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  years: number;
  months: number;
  days: number;
  olderPerson: string;
}

const DateInputGroup = ({ name, label, control }: { name: 'person1' | 'person2', label: string, control: Control<FormValues> }) => {
  const years = Array.from({ length: 121 }, (_, i) => new Date().getFullYear() - i);
  const months = Array.from({ length: 12 }, (_, i) => i + 1);
  const days = Array.from({ length: 31 }, (_, i) => i + 1);

  return (
    <div className="space-y-2 p-4 border rounded-lg">
      <FormLabel className="font-semibold">{label}</FormLabel>
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
        <FormField name={`${name}.day`} control={control} render={({ field }) => (
          <FormItem>
            <Select onValueChange={field.onChange} value={field.value?.toString()}>
              <FormControl><SelectTrigger><SelectValue placeholder="يوم" /></SelectTrigger></FormControl>
              <SelectContent>{days.map(d => <SelectItem key={d} value={String(d)}>{d}</SelectItem>)}</SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}/>
        <FormField name={`${name}.month`} control={control} render={({ field }) => (
          <FormItem>
            <Select onValueChange={field.onChange} value={field.value?.toString()}>
              <FormControl><SelectTrigger><SelectValue placeholder="شهر" /></SelectTrigger></FormControl>
              <SelectContent>{months.map(m => <SelectItem key={m} value={String(m)}>{m}</SelectItem>)}</SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}/>
        <FormField name={`${name}.year`} control={control} render={({ field }) => (
          <FormItem>
            <Select onValueChange={field.onChange} value={field.value?.toString()}>
              <FormControl><SelectTrigger><SelectValue placeholder="سنة" /></SelectTrigger></FormControl>
              <SelectContent>{years.map(y => <SelectItem key={y} value={String(y)}>{y}</SelectItem>)}</SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}/>
      </div>
    </div>
  );
};

export function AgeDifferenceCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
        person1: { day: undefined, month: undefined, year: undefined },
        person2: { day: undefined, month: undefined, year: undefined },
    }
  });

  function onSubmit(data: FormValues) {
    const date1 = new Date(data.person1.year, data.person1.month - 1, data.person1.day);
    const date2 = new Date(data.person2.year, data.person2.month - 1, data.person2.day);
    
    let olderPerson;
    let duration;

    if (isAfter(date2, date1)) { // person 1 is older
        duration = intervalToDuration({ start: date1, end: date2 });
        olderPerson = 'الشخص الأول';
    } else { // person 2 is older
        duration = intervalToDuration({ start: date2, end: date1 });
        olderPerson = 'الشخص الثاني';
    }

    setResult({
      years: duration.years || 0,
      months: duration.months || 0,
      days: duration.days || 0,
      olderPerson
    });
  }

  const getArabicPlural = (number: number, type: 'year' | 'month' | 'day') => {
    const singular = { year: 'سنة', month: 'شهر', day: 'يوم' };
    const dual = { year: 'سنتان', month: 'شهران', day: 'يومان' };
    const plural = { year: 'سنوات', month: 'أشهر', day: 'أيام' };
    const collective = { year: 'سنة', month: 'شهرًا', day: 'يومًا' };
  
    if (number === 1) return singular[type];
    if (number === 2) return dual[type];
    if (number >= 3 && number <= 10) return plural[type];
    return collective[type];
  };

  const formatDuration = (years: number, months: number, days: number) => {
    const parts = [];
    if (years > 0) {
      parts.push(`${years} ${getArabicPlural(years, 'year')}`);
    }
    if (months > 0) {
      parts.push(`${months} ${getArabicPlural(months, 'month')}`);
    }
    if (days > 0) {
      parts.push(`${days} ${getArabicPlural(days, 'day')}`);
    }

    if (parts.length === 0) return 'لا يوجد فرق في العمر';

    return parts.join(' و');
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>حساب فرق العمر بين شخصين</CardTitle>
        <CardDescription>أدخل تاريخ ميلاد شخصين لمعرفة الفرق الدقيق في العمر بينهما.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <DateInputGroup name="person1" label="تاريخ ميلاد الشخص الأول" control={form.control} />
              <DateInputGroup name="person2" label="تاريخ ميلاد الشخص الثاني" control={form.control} />
            </div>
            <Button type="submit" className="w-full">
                <Users className="ml-2 h-4 w-4" />
                احسب فرق العمر
            </Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">الفرق في العمر هو</h3>
            <div className="p-4 bg-primary/10 rounded-lg mb-4">
              <p className="text-2xl md:text-3xl font-bold text-primary">
                {formatDuration(result.years, result.months, result.days)}
              </p>
            </div>
            <p className="text-muted-foreground">{result.olderPerson} أكبر سنًا.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
