import Link from 'next/link';

export function Footer() {
  return (
    <footer className="w-full border-t bg-card text-card-foreground">
      <div className="container flex flex-col items-center justify-center gap-4 px-4 py-8 text-center md:flex-row md:justify-between md:px-6">
        <p className="text-sm text-muted-foreground">
          &copy; {new Date().getFullYear()} أدوات بالعربي. جميع الحقوق محفوظة.
        </p>
        <nav className="flex gap-4 sm:gap-6 text-sm">
          <Link href="/p/terms-of-service" className="text-muted-foreground hover:text-primary transition-colors">
            شروط الخدمة
          </Link>
          <Link href="/p/privacy-policy" className="text-muted-foreground hover:text-primary transition-colors">
            سياسة الخصوصية
          </Link>
        </nav>
      </div>
    </footer>
  );
}
