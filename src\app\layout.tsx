import type { Metadata } from 'next';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import { cn } from '@/lib/utils';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Header } from '@/components/Header';
import { AppSidebar } from '@/components/AppSidebar';
import { Footer } from '@/components/Footer';
import { JsonLd } from '@/components/JsonLd';
import type { WebSite } from 'schema-dts';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

export const metadata: Metadata = {
  metadataBase: siteUrl ? new URL(siteUrl) : undefined,
  title: {
    default: 'أدوات بالعربي - مجموعة أدوات عربية شاملة',
    template: '%s | أدوات بالعربي',
  },
  description: 'مجموعة شاملة من الأدوات والحاسبات والمحولات باللغة العربية لتسهيل حياتك اليومية.',
  keywords: ['أدوات عربية', 'حاسبة', 'محول', 'أدوات مجانية', 'مجموعة أدوات'],
  openGraph: {
    title: 'أدوات بالعربي - مجموعة أدوات عربية شاملة',
    description: 'مجموعة شاملة من الأدوات والحاسبات والمحولات باللغة العربية لتسهيل حياتك اليومية.',
    url: siteUrl,
    siteName: 'أدوات بالعربي',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'أدوات بالعربي',
      },
    ],
    locale: 'ar_SA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'أدوات بالعربي - مجموعة أدوات عربية شاملة',
    description: 'مجموعة شاملة من الأدوات والحاسبات والمحولات باللغة العربية لتسهيل حياتك اليومية.',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const jsonLd: WebSite | null = siteUrl ? {
    '@type': 'WebSite',
    name: 'أدوات بالعربي',
    url: siteUrl,
    inLanguage: 'ar',
    description: 'مجموعة شاملة من الأدوات والحاسبات والمحولات باللغة العربية لتسهيل حياتك اليومية.',
    publisher: {
      '@type': 'Organization',
      name: 'أدوات بالعربي',
      logo: {
        '@type': 'ImageObject',
        url: `${siteUrl}/logo.png`,
      }
    }
  } : null;

  return (
    <html lang="ar" dir="rtl" className="h-full">
      <head>
        {jsonLd && <JsonLd data={jsonLd} />}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Wafeq:wght@600&display=swap" rel="stylesheet" />
      </head>
      <body className={cn('font-body antialiased min-h-screen bg-background w-full h-full')}>
        <SidebarProvider>
          <div className="relative flex min-h-screen w-full flex-col">
            <Header />
            <div className="flex flex-1 w-full">
               <AppSidebar />
               <main className="flex flex-1 flex-col w-full">
                {children}
                <Footer />
              </main>
            </div>
          </div>
        </SidebarProvider>
        <Toaster />
      </body>
    </html>
  );
}
