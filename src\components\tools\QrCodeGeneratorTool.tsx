'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Download } from 'lucide-react';
import Image from 'next/image';

const FormSchema = z.object({
  data: z.string().min(1, { message: 'الرجاء إدخال نص أو رابط.' }),
});

export function QrCodeGeneratorTool() {
  const [qrCodeUrl, setQrCodeUrl] = useState('');

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zod<PERSON>esolver(FormSchema),
    mode: 'onBlur',
     defaultValues: {
      data: '',
    },
  });

  const dataToEncode = form.watch('data');

  useEffect(() => {
    if (dataToEncode && form.formState.isValid) {
      const url = `https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=${encodeURIComponent(dataToEncode)}`;
      setQrCodeUrl(url);
    } else {
      setQrCodeUrl('');
    }
  }, [dataToEncode, form.formState.isValid]);
  
  const handleDownload = () => {
    if (!qrCodeUrl) return;
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = 'qrcode.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };


  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>إنشاء رمز QR</CardTitle>
        <CardDescription>أدخل النص أو الرابط في الحقل أدناه.</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center gap-8">
        <div className="w-full">
            <Form {...form}>
              <form className="space-y-4">
                <FormField
                  control={form.control}
                  name="data"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>النص أو الرابط</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
        </div>
        
        <div className="w-[250px] h-[250px] bg-muted rounded-lg flex items-center justify-center">
            {qrCodeUrl ? (
                <Image src={qrCodeUrl} alt="Generated QR Code" width={250} height={250} className="rounded-md" />
            ): (
                <p className="text-muted-foreground text-center">سيظهر الرمز هنا</p>
            )}
        </div>
        
        {qrCodeUrl && (
            <Button onClick={handleDownload} className="w-full max-w-xs">
                <Download className="ml-2 h-4 w-4" />
                تحميل الصورة
            </Button>
        )}
      </CardContent>
    </Card>
  );
}
