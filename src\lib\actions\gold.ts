
'use server';

import { COUNTRIES_CURRENCIES, COUNTRY_CODE_TO_CURRENCY } from '@/lib/constants/currencies';
import { getIpInfo } from './ip';

// Function to get real exchange rates
async function getExchangeRate(currency: string): Promise<number> {
  // If it's USD, return 1 (base currency)
  if (currency === 'USD') {
    return 1;
  }

  try {
    const response = await fetch(
      `https://api.exchangerate-api.com/v4/latest/USD`,
      {
        next: { revalidate: 3600 }, // Revalidate every hour
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; Gold Price Tool)',
        },
      }
    );

    if (response.ok) {
      const data = await response.json();
      if (data && data.rates && data.rates[currency]) {
        return parseFloat(data.rates[currency]);
      }
    }
  } catch (error) {
    console.error('Error fetching exchange rate from ExchangeRate-API:', error);
  }

  // Fallback to approximate current rates
  const fallbackRates: Record<string, number> = {
    'SAR': 3.75, 'AED': 3.67, 'KWD': 0.31, 'BHD': 0.38, 'OMR': 0.38,
    'QAR': 3.64, 'EGP': 49.50, 'JOD': 0.71, 'LBP': 15000, 'SYP': 13000,
    'IQD': 1300, 'YER': 250, 'SDG': 600, 'LYD': 4.85, 'TND': 3.15,
    'MAD': 10.20, 'DZD': 134.50, 'MRU': 39.70, 'SOS': 570, 'DJF': 177,
    'KMF': 465, 'EUR': 0.92, 'USD': 1.0
  };
  
  return fallbackRates[currency] || 1;
}

export async function getGoldPrices(requestedCurrency?: string) {
  let selectedCurrency = 'SAR'; // Default
  let error;

  if (requestedCurrency) {
    selectedCurrency = requestedCurrency;
  } else {
    // Detect currency from IP
    const ipInfo = await getIpInfo();
    error = ipInfo.error;
    if (ipInfo.countryCode && COUNTRY_CODE_TO_CURRENCY[ipInfo.countryCode]) {
      selectedCurrency = COUNTRY_CODE_TO_CURRENCY[ipInfo.countryCode];
    }
  }
  
  // التحقق من أن العملة مدعومة
  if (!COUNTRIES_CURRENCIES[selectedCurrency as keyof typeof COUNTRIES_CURRENCIES]) {
    selectedCurrency = 'SAR';
  }
  
  const exchangeRate = await getExchangeRate(selectedCurrency);
  
  try {
    const response = await fetch(
      'https://query1.finance.yahoo.com/v8/finance/chart/GC=F',
      {
        next: { revalidate: 900 }, // Revalidate every 15 minutes
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; Gold Price Tool)',
        },
      }
    );

    if (response.ok) {
      const data = await response.json();
      const meta = data?.chart?.result?.[0]?.meta;
      if (meta?.regularMarketPrice) {
        const goldPriceUSD = parseFloat(meta.regularMarketPrice);
        
        if (!isNaN(goldPriceUSD) && goldPriceUSD > 1000) {
          const goldPriceLocal = goldPriceUSD * exchangeRate;
          const goldPricePerGramUSD = goldPriceUSD / 31.1035;
          const goldPricePerGramLocal = goldPricePerGramUSD * exchangeRate;
          const goldPricePerKiloUSD = goldPricePerGramUSD * 1000;
          const goldPricePerKiloLocal = goldPricePerGramLocal * 1000;
          
          const karatPrices = {
            '24K': goldPricePerGramLocal,
            '22K': goldPricePerGramLocal * (22/24),
            '21K': goldPricePerGramLocal * (21/24),
            '18K': goldPricePerGramLocal * (18/24),
          };

          return {
            success: true,
            timestamp: new Date().toISOString(),
            selectedCurrency,
            countryInfo: COUNTRIES_CURRENCIES[selectedCurrency as keyof typeof COUNTRIES_CURRENCIES],
            prices: {
              usd: { perOunce: goldPriceUSD, perGram: goldPricePerGramUSD, perKilo: goldPricePerKiloUSD },
              local: { perOunce: goldPriceLocal, perGram: goldPricePerGramLocal, perKilo: goldPricePerKiloLocal },
              karats: karatPrices,
            },
            source: 'Yahoo Finance',
            error,
          };
        }
      }
    }
  } catch (e) {
    console.error('Error fetching from Yahoo Finance:', e);
  }

  // Fallback data
  const fallbackPriceUSD = 2658.75;
  const goldPriceLocal = fallbackPriceUSD * exchangeRate;
  const goldPricePerGramUSD = fallbackPriceUSD / 31.1035;
  const goldPricePerGramLocal = goldPricePerGramUSD * exchangeRate;
  const goldPricePerKiloUSD = goldPricePerGramUSD * 1000;
  const goldPricePerKiloLocal = goldPricePerGramLocal * 1000;
  
  const karatPrices = {
    '24K': goldPricePerGramLocal, '22K': goldPricePerGramLocal * (22/24),
    '21K': goldPricePerGramLocal * (21/24), '18K': goldPricePerGramLocal * (18/24),
  };

  return {
    success: true,
    timestamp: new Date().toISOString(),
    selectedCurrency,
    countryInfo: COUNTRIES_CURRENCIES[selectedCurrency as keyof typeof COUNTRIES_CURRENCIES],
    prices: {
      usd: { perOunce: fallbackPriceUSD, perGram: goldPricePerGramUSD, perKilo: goldPricePerKiloUSD },
      local: { perOunce: goldPriceLocal, perGram: goldPricePerGramLocal, perKilo: goldPricePerKiloLocal },
      karats: karatPrices,
    },
    source: 'مصادر محدثة (Fallback)',
    error: error || 'الأسعار تقديرية حديثة - قد تختلف عن الأسعار اللحظية',
  };
}
